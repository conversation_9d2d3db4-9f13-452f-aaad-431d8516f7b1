<script lang="ts" setup>
import { DocInput } from "@tg-fe/ui";
import { ref } from "vue";
import UploadButton from "../../other/UploadButton.vue";

const list = ref<(string | undefined)[]>([undefined]);

const handleAddText = () => {
  list.value.push(undefined);
};
const handleOk = () => {
  list.value.forEach((item) => console.log(item));
};
</script>

<template>
  <div>
    <div class="h-[428px] space-y-3 overflow-y-auto">
      <div class="text-text-icon-text-3 text-xs">请在此处粘贴文本</div>
      <DocInput
        v-for="item in list"
        v-model="item"
        :key="item"
        type="textarea"
        placeholder="粘贴文本"
        :rows="list.length > 1 ? 2 : 16"
      />

      <UploadButton
        :disabled="list.length >= 50"
        :tooltip="{ content: '最多添加50条文本', disabled: list.length < 50 }"
        @click="handleAddText"
      >
        添加文本
      </UploadButton>
    </div>
    <div class="flex justify-end pt-6">
      <el-button color="black" @click="handleOk">确定</el-button>
    </div>
  </div>
</template>
