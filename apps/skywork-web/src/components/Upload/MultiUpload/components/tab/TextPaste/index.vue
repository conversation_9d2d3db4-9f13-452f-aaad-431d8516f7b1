<script lang="ts" setup>
import { DocInput } from "@tg-fe/ui";
import { ref } from "vue";
import { ElButton } from "element-plus";
import UploadButton from "../../other/UploadButton.vue";

interface TextItem {
  id: number;
  value: string;
}

let nextId = 1;

const list = ref<TextItem[]>([{ id: nextId++, value: "" }]);

const handleAddText = () => {
  list.value.push({ id: nextId++, value: "" });
};

const handleOk = () => {
  console.log(list.value);
};
</script>

<template>
  <div>
    <div class="h-[428px] space-y-3 overflow-y-auto">
      <div class="text-text-icon-text-3 text-xs">请在此处粘贴文本</div>
      <div class="flex items-center" v-for="item in list" :key="item.id">
        <DocInput v-model="item.value" type="textarea" placeholder="粘贴文本" :rows="list.length > 1 ? 2 : 16" />
        <SvgIcon name="minus-cirlce"></SvgIcon>
      </div>

      <UploadButton
        :disabled="list.length >= 50"
        :tooltip="{ content: '最多添加50条文本', disabled: list.length < 50 }"
        @click="handleAddText"
      >
        添加文本
      </UploadButton>
    </div>
    <div class="flex justify-end pt-6">
      <ElButton color="black" @click="handleOk">确定</ElButton>
    </div>
  </div>
</template>
